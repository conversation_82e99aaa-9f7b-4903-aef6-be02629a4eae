# EODHD Search API Integration

This document describes the EODHD Search API integration implemented in Portavio for enhanced ticker search functionality.

## Overview

The EODHD integration provides real-time ticker search capabilities that prioritize external market data over local database content, while maintaining seamless fallback to database search when needed.

## Features

- **Real-time ticker search** using EODHD Search API
- **Intelligent fallback** to database search when EODHD is unavailable
- **Asset existence checking** to determine if tickers already exist in the database
- **Automatic asset creation** for new tickers selected from EODHD results
- **Visual indicators** showing data source (EODHD vs Database) and asset status
- **Error handling** with Romanian error messages

## Architecture

### Components

1. **EODHD Search Client** (`src/lib/eodhd-search-client.ts`)
   - Handles API communication with EODHD
   - Processes and normalizes search results
   - Provides error handling and timeout management

2. **Enhanced Asset Search Hook** (`src/hooks/use-asset-search.ts`)
   - Combines EODHD and database search results
   - Manages search state and loading indicators
   - Implements intelligent search prioritization

3. **Updated Ticker Search Component** (`src/components/transactions/ticker-search.tsx`)
   - Enhanced UI with source indicators
   - Automatic asset creation flow
   - Improved user experience with status badges

4. **API Endpoints**
   - `/api/search/eodhd` - EODHD search proxy
   - `/api/assets/check-exists` - Single ticker existence check
   - `/api/assets/check-exists-batch` - Batch ticker existence check

### Search Flow

```mermaid
graph TD
    A[User types search term] --> B{EODHD enabled?}
    B -->|Yes| C[Search EODHD API]
    B -->|No| D[Search Database]
    C --> E{EODHD results found?}
    E -->|Yes| F[Check which tickers exist in DB]
    E -->|No| D
    F --> G[Mark existing/new tickers]
    G --> H[Display combined results]
    D --> I[Display database results]
    H --> J[User selects ticker]
    I --> J
    J --> K{Ticker exists in DB?}
    K -->|Yes| L[Proceed with transaction]
    K -->|No| M[Trigger asset creation]
    M --> N[Create asset via external API]
    N --> L
```

## Configuration

### Environment Variables

Add the following to your `.env` file:

```env
# EODHD API Configuration
EODHD_API_TOKEN=your_eodhd_api_token_here
```

### Usage Options

The search behavior can be configured via the `useAssetSearch` hook:

```typescript
const { assets, loading, error, searchSource } = useAssetSearch({
  debounceMs: 300,           // Search debounce delay
  minSearchLength: 1,        // Minimum characters to trigger search
  maxResults: 15,            // Maximum results to return
  prioritizeEODHD: true,     // Whether to prioritize EODHD over database
});
```

## API Reference

### EODHD Search Client

```typescript
import { createEODHDSearchClient } from '@/lib/eodhd-search-client';

const client = createEODHDSearchClient();
const results = await client.searchTickers('AAPL');
```

### Asset Existence Check

```typescript
import { checkAssetExists, checkMultipleAssetsExist } from '@/lib/asset-existence-check';

// Single ticker check
const exists = await checkAssetExists('AAPL');

// Multiple tickers check
const existenceMap = await checkMultipleAssetsExist(['AAPL', 'MSFT', 'GOOGL']);
```

### API Endpoints

#### Search EODHD
```http
GET /api/search/eodhd?q=AAPL&limit=10
```

Response:
```json
{
  "success": true,
  "data": [
    {
      "ticker": "AAPL",
      "name": "Apple Inc",
      "displayTicker": "AAPL.US",
      "description": "Apple Inc • US • USA",
      "exchange": "US",
      "type": "Common Stock",
      "country": "USA",
      "currency": "USD",
      "previousClose": 150.25
    }
  ],
  "count": 1,
  "source": "eodhd"
}
```

#### Check Asset Existence
```http
GET /api/assets/check-exists?ticker=AAPL
```

Response:
```json
{
  "success": true,
  "exists": true,
  "ticker": "AAPL"
}
```

#### Batch Check Asset Existence
```http
POST /api/assets/check-exists-batch
Content-Type: application/json

{
  "tickers": ["AAPL", "MSFT", "NEWSTOCK"]
}
```

Response:
```json
{
  "success": true,
  "results": [
    { "ticker": "AAPL", "exists": true },
    { "ticker": "MSFT", "exists": true },
    { "ticker": "NEWSTOCK", "exists": false }
  ],
  "count": 3
}
```

## User Experience

### Visual Indicators

- **Blue icon with "Existent" badge**: Ticker exists in database
- **Orange icon with "Nou" badge**: New ticker from EODHD (will trigger asset creation)
- **Database icon**: Result from local database
- **Globe icon**: Result from EODHD API

### Asset Creation Flow

When a user selects a ticker that doesn't exist in the database:

1. **Automatic Detection**: System detects the ticker is new
2. **Asset Creation**: Triggers the existing asset creation service
3. **Progress Tracking**: Shows creation progress with Romanian messages
4. **Completion**: Asset is created and transaction can proceed

## Error Handling

All error messages are displayed in Romanian:

- **API Failures**: "A apărut o eroare la căutarea simbolurilor"
- **Network Issues**: "Căutarea a expirat. Încearcă din nou."
- **Invalid Input**: "Parametri invalizi"
- **Service Unavailable**: "Serviciul de căutare nu este configurat corect"

## Testing

Run the integration tests:

```bash
node scripts/test-eodhd-integration.js
```

This will test:
- EODHD client functionality
- API endpoint responses
- Error handling
- Asset existence checking

## Performance Considerations

- **Debounced Search**: 300ms delay to prevent excessive API calls
- **Request Cancellation**: Automatic cancellation of outdated requests
- **Caching**: Database results are used to mark existing assets
- **Timeout Handling**: 10-second timeout for EODHD requests
- **Fallback Strategy**: Graceful degradation to database search

## Security

- **API Key Protection**: EODHD API token is server-side only
- **Input Validation**: All search terms are validated and sanitized
- **Rate Limiting**: Respects EODHD API rate limits
- **Error Sanitization**: Sensitive error details are not exposed to client

## Troubleshooting

### Common Issues

1. **"EODHD_API_TOKEN environment variable is required"**
   - Add your EODHD API token to the `.env` file

2. **Search returns no results**
   - Check if EODHD API is accessible
   - Verify API token is valid
   - Check network connectivity

3. **Asset creation fails**
   - Ensure external API endpoints are configured
   - Check database connectivity
   - Verify user permissions

### Debug Mode

Enable debug logging by setting:
```env
DEBUG=eodhd:*
```

This will log all EODHD API requests and responses for troubleshooting.
