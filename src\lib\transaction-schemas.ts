import { z } from "zod";

// Date utility helpers
export const getTodayDateString = (): string => {
  const today = new Date();
  return (
    today.getFullYear() +
    "-" +
    String(today.getMonth() + 1).padStart(2, "0") +
    "-" +
    String(today.getDate()).padStart(2, "0")
  );
};

export const getYesterdayDateString = (): string => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return (
    yesterday.getFullYear() +
    "-" +
    String(yesterday.getMonth() + 1).padStart(2, "0") +
    "-" +
    String(yesterday.getDate()).padStart(2, "0")
  );
};

// Transaction Types
export const TRANSACTION_TYPES = ["BUY", "SELL"] as const;
export type TransactionType = (typeof TRANSACTION_TYPES)[number];

// Transaction Form Schema
export const transactionFormSchema = z.object({
  // Transaction type - fixed to BUY for now
  type: z.enum(TRANSACTION_TYPES),

  // Ticker symbol - required, trimmed, uppercase
  ticker: z
    .string()
    .min(1, "Simbolul este obligatoriu")
    .max(20, "Simbolul nu poate avea mai mult de 20 de caractere")
    .transform((val) => val.trim().toUpperCase())
    .refine((val) => val.length > 0, "Simbolul nu poate fi gol")
    .refine(
      (val) => /^[A-Z0-9.-]+$/.test(val),
      "Simbolul poate conține doar litere, cifre, puncte și liniuțe"
    ),

  // Quantity - positive decimal with up to 8 decimal places
  quantity: z
    .number({
      required_error: "Cantitatea este obligatorie",
      invalid_type_error: "Cantitatea trebuie să fie un număr",
    })
    .positive("Cantitatea trebuie să fie pozitivă")
    .max(999999999, "Cantitatea este prea mare")
    .refine((val) => {
      // Check if the number has at most 8 decimal places
      const str = val.toString();
      const decimalIndex = str.indexOf(".");
      if (decimalIndex === -1) return true;
      return str.length - decimalIndex - 1 <= 8;
    }, "Cantitatea poate avea maximum 8 zecimale"),

  // Transaction date - required, cannot be in the future (YYYY-MM-DD format)
  transactionDate: z
    .string({
      required_error: "Data tranzacției este obligatorie",
      invalid_type_error: "Data nu este validă",
    })
    .min(1, "Data tranzacției este obligatorie")
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Data trebuie să fie în formatul YYYY-MM-DD")
    .refine((dateStr) => {
      // Validate that it's a valid date
      const date = new Date(dateStr + "T00:00:00.000Z"); // Parse as UTC to avoid timezone issues
      return !isNaN(date.getTime());
    }, "Data nu este validă")
    .refine((dateStr) => {
      // Check that date is not in the future
      const inputDate = new Date(dateStr + "T00:00:00.000Z"); // Parse as UTC
      const todayStr = getTodayDateString();
      const todayDate = new Date(todayStr + "T00:00:00.000Z"); // Parse as UTC
      return inputDate < todayDate;
    }, "Data tranzacției nu poate fi în viitor"),

  // Portfolio ID - required when user has multiple portfolios
  portfolioId: z
    .string()
    .min(1, "Portofoliul este obligatoriu")
    .uuid("ID-ul portofoliului nu este valid"),

  // Optional notes
  notes: z
    .string()
    .max(1000, "Notele nu pot avea mai mult de 1000 de caractere")
    .optional(),
});

// Form data type
export type TransactionFormData = z.infer<typeof transactionFormSchema>;

// Input schema for creating transactions (before form processing)
export const transactionInputSchema = z.object({
  type: z.enum(TRANSACTION_TYPES).default("BUY"),
  ticker: z.string().min(1, "Simbolul este obligatoriu"),
  quantity: z.union([
    z.number(),
    z.string().transform((val) => {
      const num = parseFloat(val);
      if (isNaN(num)) throw new Error("Cantitatea trebuie să fie un număr");
      return num;
    }),
  ]),
  transactionDate: z.string().min(1, "Data tranzacției este obligatorie"),
  portfolioId: z.string().min(1, "Portofoliul este obligatoriu"),
  notes: z.string().optional(),
});

export type TransactionInputData = z.infer<typeof transactionInputSchema>;

// Portfolio selection schema
export const portfolioSelectionSchema = z.object({
  portfolioId: z.string().uuid("ID-ul portofoliului nu este valid"),
});

export type PortfolioSelectionData = z.infer<typeof portfolioSelectionSchema>;

// Validation helpers
export const validateTicker = (ticker: string): boolean => {
  return /^[A-Z0-9.-]+$/.test(ticker.trim().toUpperCase());
};

export const validateTransactionDate = (dateStr: string): boolean => {
  // Check format
  if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return false;

  // Check if it's a valid date
  const date = new Date(dateStr + "T00:00:00.000Z");
  if (isNaN(date.getTime())) return false;

  // Check if it's not in the future
  const todayStr = getTodayDateString();
  const todayDate = new Date(todayStr + "T00:00:00.000Z");

  return date <= todayDate;
};

export const validateQuantity = (quantity: number): boolean => {
  if (quantity <= 0) return false;
  if (quantity > 999999999) return false;

  // Check decimal places
  const str = quantity.toString();
  const decimalIndex = str.indexOf(".");
  if (decimalIndex !== -1 && str.length - decimalIndex - 1 > 8) {
    return false;
  }

  return true;
};

// Format helpers
export const formatQuantity = (quantity: number): string => {
  // Format with up to 8 decimal places, removing trailing zeros
  return quantity.toFixed(8).replace(/\.?0+$/, "");
};

export const formatTransactionDate = (dateStr: string): string => {
  const date = new Date(dateStr + "T00:00:00.000Z");
  return date.toLocaleDateString("ro-RO");
};

// Default values
export const getDefaultTransactionFormData = (
  portfolioId?: string
): Partial<TransactionFormData> => {
  return {
    type: "BUY" as const,
    ticker: "",
    quantity: undefined,
    transactionDate: getYesterdayDateString(),
    portfolioId: portfolioId || "",
    notes: "",
  };
};
