# Portfolios Feature

This directory contains all components and functionality related to the portfolios feature in Portavio.

## Components

### Core Components

- **`portfolios-client.tsx`** - Main client-side component that handles data fetching, state management, and renders the portfolios page
- **`portfolio-card.tsx`** - Individual portfolio card component displaying portfolio information and metrics
- **`portfolio-grid.tsx`** - Grid layout component for displaying multiple portfolio cards

### UI State Components

- **`portfolio-skeleton.tsx`** - Loading skeleton components for portfolios
- **`empty-portfolios-state.tsx`** - Empty state when user has no portfolios
- **`portfolio-error-state.tsx`** - Error state with retry functionality

## Features

### Portfolio Metrics

The portfolios page displays comprehensive metrics for each portfolio:

- **Total Holdings** - Number of active positions (stocks with quantity > 0)
- **Total Transactions** - Total number of buy/sell transactions
- **Top Holdings** - Up to 3 largest positions by quantity
- **Last Activity** - Date of most recent transaction

### Responsive Design

- Mobile-first responsive grid layout
- Adapts from 1 column on mobile to 3 columns on desktop
- Proper touch targets and accessibility

### Loading States

- Skeleton loading during data fetch
- Strategic loading.tsx placement for Next.js App Router
- Smooth transitions between states

### Error Handling

- Comprehensive error states with retry functionality
- User-friendly error messages in Romanian
- Graceful fallbacks for network issues

## API Integration

### Endpoints Used

- **`GET /api/portfolios/metrics`** - Fetches portfolios with calculated metrics
- **`POST /api/portfolios/ensure`** - Ensures user has at least one portfolio

### Data Flow

1. Page loads with authentication check
2. Client component fetches portfolio data with metrics
3. Metrics are calculated server-side for performance
4. UI updates with loading → data/empty/error states

## Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- Focus management
- Semantic HTML structure

## Performance

- Server-side metric calculations
- Efficient data fetching
- Optimized re-renders
- Lazy loading of components

## Future Enhancements

- Individual portfolio detail pages
- Portfolio performance charts
- Portfolio comparison features
- Export functionality
- Advanced filtering and sorting
