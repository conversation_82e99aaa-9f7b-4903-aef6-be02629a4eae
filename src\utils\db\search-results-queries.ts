import { hasuraQuery, hasuraMutation } from "./hasura";

// Types
export interface SearchResult {
  id: number;
  code: string;
  exchange: string;
  name: string;
  type: string;
  country: string;
  currency: string;
  isin?: string;
  created_at: string;
  updated_at: string;
}

export interface SearchResultInput {
  code: string;
  exchange: string;
  name: string;
  type: string;
  country: string;
  currency: string;
  isin?: string;
}

// GraphQL Queries

/**
 * Get all cached search results (for debugging/admin purposes)
 */
export const GET_ALL_SEARCH_RESULTS = `
  query GetAllSearchResults($limit: Int = 100) {
    ptvuser_search_results(
      order_by: {created_at: desc}
      limit: $limit
    ) {
      id
      code
      exchange
      name
      type
      country
      currency
      isin
      created_at
      updated_at
    }
  }
`;

/**
 * Get search results count
 */
export const GET_SEARCH_RESULTS_COUNT = `
  query GetSearchResultsCount {
    ptvuser_search_results_aggregate {
      aggregate {
        count
      }
    }
  }
`;

/**
 * Search cached results by code, name, or exchange
 */
export const SEAR<PERSON>_CACHED_RESULTS = `
  query SearchCachedResults($searchTerm: String!, $limit: Int = 15) {
    ptvuser_search_results(
      where: {
        _or: [
          { code: { _ilike: $searchTerm } },
          { name: { _ilike: $searchTerm } }
        ]
      }
      order_by: [
        { code: asc },
        { name: asc }
      ]
      limit: $limit
    ) {
      id
      code
      exchange
      name
      type
      country
      currency
      isin
      created_at
      updated_at
    }
  }
`;

// GraphQL Mutations

/**
 * Insert multiple search results with conflict resolution (upsert)
 * Uses ON CONFLICT to handle duplicates based on code + exchange
 */
export const UPSERT_SEARCH_RESULTS = `
  mutation UpsertSearchResults($objects: [ptvuser_search_results_insert_input!]!) {
    insert_ptvuser_search_results(
      objects: $objects,
      on_conflict: {
        constraint: search_results_code_exchange_unique,
        update_columns: [name, type, country, currency, isin, updated_at]
      }
    ) {
      affected_rows
      returning {
        id
        code
        exchange
        name
        type
        country
        currency
        isin
        created_at
        updated_at
      }
    }
  }
`;

/**
 * Insert a single search result with conflict resolution (upsert)
 */
export const UPSERT_SEARCH_RESULT = `
  mutation UpsertSearchResult($object: ptvuser_search_results_insert_input!) {
    insert_ptvuser_search_results_one(
      object: $object,
      on_conflict: {
        constraint: search_results_code_exchange_unique,
        update_columns: [name, type, country, currency, isin, updated_at]
      }
    ) {
      id
      code
      exchange
      name
      type
      country
      currency
      isin
      created_at
      updated_at
    }
  }
`;

/**
 * Delete old search results to maintain cache size limit
 * Keeps only the most recent N results
 */
export const CLEANUP_OLD_SEARCH_RESULTS = `
  mutation CleanupOldSearchResults($keepCount: Int = 15) {
    delete_ptvuser_search_results(
      where: {
        id: {
          _nin: {
            _query: {
              query: "SELECT id FROM ptvuser.search_results ORDER BY created_at DESC LIMIT $1",
              variables: [$keepCount]
            }
          }
        }
      }
    ) {
      affected_rows
    }
  }
`;

/**
 * Alternative cleanup using subquery (if the above doesn't work)
 */
export const CLEANUP_OLD_SEARCH_RESULTS_ALT = `
  mutation CleanupOldSearchResultsAlt($keepCount: Int = 15) {
    delete_ptvuser_search_results(
      where: {
        created_at: {
          _lt: {
            _query: {
              query: "SELECT created_at FROM ptvuser.search_results ORDER BY created_at DESC LIMIT 1 OFFSET $1",
              variables: [$keepCount]
            }
          }
        }
      }
    ) {
      affected_rows
    }
  }
`;

// Utility Functions

/**
 * Cache multiple search results from EODHD API response
 */
export async function cacheSearchResults(
  searchResults: SearchResultInput[]
): Promise<SearchResult[]> {
  try {
    if (searchResults.length === 0) {
      return [];
    }

    const result = await hasuraMutation<{
      insert_ptvuser_search_results: {
        affected_rows: number;
        returning: SearchResult[];
      };
    }>(UPSERT_SEARCH_RESULTS, {
      variables: {
        objects: searchResults.map(result => ({
          code: result.code,
          exchange: result.exchange,
          name: result.name,
          type: result.type,
          country: result.country,
          currency: result.currency,
          isin: result.isin || null,
        })),
      },
    });

    return result.insert_ptvuser_search_results.returning || [];
  } catch (error) {
    console.error("Error caching search results:", error);
    throw new Error("Nu s-au putut salva rezultatele căutării în cache");
  }
}

/**
 * Cache a single search result
 */
export async function cacheSingleSearchResult(
  searchResult: SearchResultInput
): Promise<SearchResult> {
  try {
    const result = await hasuraMutation<{
      insert_ptvuser_search_results_one: SearchResult;
    }>(UPSERT_SEARCH_RESULT, {
      variables: {
        object: {
          code: searchResult.code,
          exchange: searchResult.exchange,
          name: searchResult.name,
          type: searchResult.type,
          country: searchResult.country,
          currency: searchResult.currency,
          isin: searchResult.isin || null,
        },
      },
    });

    return result.insert_ptvuser_search_results_one;
  } catch (error) {
    console.error("Error caching single search result:", error);
    throw new Error("Nu s-a putut salva rezultatul căutării în cache");
  }
}

/**
 * Get count of cached search results
 */
export async function getSearchResultsCount(): Promise<number> {
  try {
    const result = await hasuraQuery<{
      ptvuser_search_results_aggregate: {
        aggregate: {
          count: number;
        };
      };
    }>(GET_SEARCH_RESULTS_COUNT);

    return result.ptvuser_search_results_aggregate.aggregate.count || 0;
  } catch (error) {
    console.error("Error getting search results count:", error);
    return 0;
  }
}

/**
 * Clean up old search results to maintain cache size limit
 */
export async function cleanupOldSearchResults(keepCount: number = 15): Promise<number> {
  try {
    // First, get the current count
    const currentCount = await getSearchResultsCount();
    
    if (currentCount <= keepCount) {
      return 0; // No cleanup needed
    }

    // Use a simpler approach: get IDs to keep and delete the rest
    const keepResults = await hasuraQuery<{
      ptvuser_search_results: { id: number }[];
    }>(`
      query GetRecentSearchResults($limit: Int!) {
        ptvuser_search_results(
          order_by: {created_at: desc}
          limit: $limit
        ) {
          id
        }
      }
    `, {
      variables: { limit: keepCount }
    });

    const idsToKeep = keepResults.ptvuser_search_results.map(r => r.id);

    if (idsToKeep.length === 0) {
      return 0;
    }

    // Delete records not in the keep list
    const deleteResult = await hasuraMutation<{
      delete_ptvuser_search_results: {
        affected_rows: number;
      };
    }>(`
      mutation DeleteOldSearchResults($idsToKeep: [Int!]!) {
        delete_ptvuser_search_results(
          where: {
            id: { _nin: $idsToKeep }
          }
        ) {
          affected_rows
        }
      }
    `, {
      variables: { idsToKeep }
    });

    return deleteResult.delete_ptvuser_search_results.affected_rows || 0;
  } catch (error) {
    console.error("Error cleaning up old search results:", error);
    throw new Error("Nu s-au putut curăța rezultatele vechi din cache");
  }
}

/**
 * Search in cached results
 */
export async function searchCachedResults(
  searchTerm: string,
  limit: number = 15
): Promise<SearchResult[]> {
  try {
    // Add wildcards for partial matching
    const searchPattern = `%${searchTerm}%`;

    const result = await hasuraQuery<{
      ptvuser_search_results: SearchResult[];
    }>(SEARCH_CACHED_RESULTS, {
      variables: {
        searchTerm: searchPattern,
        limit,
      },
    });

    return result.ptvuser_search_results || [];
  } catch (error) {
    console.error("Error searching cached results:", error);
    throw new Error("Nu s-au putut căuta rezultatele din cache");
  }
}
