/**
 * Asset Creation Service
 * Handles the complete flow of creating a new asset by calling the external API endpoints
 */

import {
  createExternalAPIClient,
  ExternalAPIClient,
  AssetResponse,
  AssetWithMetricsResponse,
  AssetWithDividendsResponse,
  ExternalAPIError,
  ExternalAPIValidationError,
} from "./external-api-client";
import { normalizeTicker, isValidTickerFormat } from "./ticker-utils";

// Service Response Types
export interface AssetCreationResult {
  success: true;
  asset: AssetResponse;
  hasHistory: boolean;
  hasDividends: boolean;
  latestPrice?: number;
  currency?: string;
}

export interface AssetCreationError {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

export type AssetCreationResponse = AssetCreationResult | AssetCreationError;

// Progress tracking types
export interface AssetCreationProgress {
  step:
    | "validating"
    | "fetching_data"
    | "fetching_history"
    | "fetching_dividends"
    | "completed"
    | "error";
  message: string;
  progress: number; // 0-100
  details?: string;
}

export type ProgressCallback = (progress: AssetCreationProgress) => void;

// Service Configuration
interface AssetCreationOptions {
  refresh?: boolean;
  useMock?: boolean;
  daysHistory?: number;
  yearsHistoryDividend?: number;
  providers?: string[];
  onProgress?: ProgressCallback;
}

/**
 * Asset Creation Service
 * Orchestrates the sequential API calls to create a complete asset record
 */
export class AssetCreationService {
  private apiClient: ExternalAPIClient;

  constructor(apiClient?: ExternalAPIClient) {
    this.apiClient = apiClient || createExternalAPIClient();
  }

  /**
   * Emit progress update if callback is provided
   */
  private emitProgress(
    onProgress: ProgressCallback | undefined,
    step: AssetCreationProgress["step"],
    message: string,
    progress: number,
    details?: string
  ) {
    if (onProgress) {
      onProgress({
        step,
        message,
        progress,
        details,
      });
    }
  }

  /**
   * Create a new asset by calling all three API endpoints sequentially
   */
  async createAsset(
    ticker: string,
    options: AssetCreationOptions = {}
  ): Promise<AssetCreationResponse> {
    const { onProgress } = options;

    try {
      // Step 1: Validate ticker format
      this.emitProgress(
        onProgress,
        "validating",
        "Se validează formatul simbolului...",
        10,
        `Verificare simbol: ${ticker}`
      );

      if (!isValidTickerFormat(ticker)) {
        this.emitProgress(
          onProgress,
          "error",
          "Formatul simbolului nu este valid",
          0,
          `Simbolul "${ticker}" nu respectă formatul cerut`
        );
        return {
          success: false,
          error: "Formatul simbolului nu este valid",
          code: "INVALID_TICKER_FORMAT",
        };
      }

      // Handle .US suffix tickers - remove suffix for processing
      const originalTicker = ticker;
      const isUSTicker = ticker.toUpperCase().endsWith(".US");
      const processedTicker = isUSTicker ? ticker.slice(0, -3) : ticker;
      const normalizedTicker = normalizeTicker(processedTicker);

      console.log(
        `Processing ticker: ${originalTicker} -> ${normalizedTicker}${
          isUSTicker ? " (US ticker detected)" : ""
        }`
      );

      // Default options
      const defaultOptions: AssetCreationOptions = {
        refresh: true, // Always refresh when creating new assets
        useMock: false,
        daysHistory: 30,
        yearsHistoryDividend: 5,
        ...options,
      };

      // Step 2: Get basic asset data
      this.emitProgress(
        onProgress,
        "fetching_data",
        "Se obțin datele de bază ale activului...",
        25,
        `Preluare informații pentru ${normalizedTicker}`
      );

      console.log(
        `Creating asset for ticker: ${normalizedTicker} - Step 2: Getting asset data${
          isUSTicker ? " (using yfinance only)" : ""
        }`
      );

      let assetData: AssetResponse;
      try {
        // For .US tickers, restrict to yfinance provider only for asset data
        const assetDataProviders = isUSTicker
          ? ["yfinance"]
          : defaultOptions.providers;

        assetData = await this.apiClient.getAssetData(normalizedTicker, {
          refresh: defaultOptions.refresh,
          useMock: defaultOptions.useMock,
          providers: assetDataProviders,
        });
      } catch (error) {
        this.emitProgress(
          onProgress,
          "error",
          "Nu s-au putut obține datele de bază ale activului",
          25,
          error instanceof Error ? error.message : "Eroare necunoscută"
        );
        return this.handleAPIError(
          error,
          "Nu s-au putut obține datele de bază ale activului"
        );
      }

      // Step 3: Get price history and metrics
      this.emitProgress(
        onProgress,
        "fetching_history",
        "Se obțin datele de preț și metrici...",
        50,
        `Preluare istoric prețuri pentru ${normalizedTicker}`
      );

      console.log(
        `Creating asset for ticker: ${normalizedTicker} - Step 3: Getting price history`
      );

      let historyData: AssetWithMetricsResponse | null = null;
      let hasHistory = false;

      try {
        historyData = await this.apiClient.getAssetHistory(normalizedTicker, {
          refresh: defaultOptions.refresh,
          useMock: defaultOptions.useMock,
          daysHistory: defaultOptions.daysHistory,
          providers: defaultOptions.providers,
        });
        hasHistory = true;
      } catch (error) {
        console.warn(`Failed to get history for ${normalizedTicker}:`, error);
        // Continue without history data - this is not critical for asset creation
      }

      // Step 4: Get dividend data
      this.emitProgress(
        onProgress,
        "fetching_dividends",
        "Se obțin datele despre dividende...",
        75,
        `Preluare istoric dividende pentru ${normalizedTicker}`
      );

      console.log(
        `Creating asset for ticker: ${normalizedTicker} - Step 4: Getting dividend data`
      );

      let dividendData: AssetWithDividendsResponse | null = null;
      let hasDividends = false;

      try {
        dividendData = await this.apiClient.getAssetDividends(
          normalizedTicker,
          {
            refresh: defaultOptions.refresh,
            useMock: defaultOptions.useMock,
            yearsHistoryDividend: defaultOptions.yearsHistoryDividend,
            providers: defaultOptions.providers,
          }
        );
        hasDividends =
          dividendData.dividends && dividendData.dividends.length > 0;
      } catch (error) {
        console.warn(`Failed to get dividends for ${normalizedTicker}:`, error);
        // Continue without dividend data - this is not critical for asset creation
      }

      // Determine latest price from history data
      let latestPrice: number | undefined;
      if (historyData?.asset.prices && historyData.asset.prices.length > 0) {
        // Sort by date descending and get the most recent price
        const sortedPrices = historyData.asset.prices.sort(
          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        latestPrice = sortedPrices[0].close_price;
      }

      // Get currency code
      const currency =
        assetData.currency?.code || historyData?.asset.currency?.code;

      // Step 5: Completion
      this.emitProgress(
        onProgress,
        "completed",
        "Activul a fost creat cu succes!",
        100,
        `${normalizedTicker} - ${assetData.name || "Activ nou"}`
      );

      console.log(`Asset creation completed for ${normalizedTicker}:`, {
        hasBasicData: true,
        hasHistory,
        hasDividends,
        latestPrice,
        currency,
      });

      return {
        success: true,
        asset: assetData,
        hasHistory,
        hasDividends,
        latestPrice,
        currency,
      };
    } catch (error) {
      console.error("Unexpected error in asset creation:", error);

      this.emitProgress(
        onProgress,
        "error",
        "A apărut o eroare neașteptată",
        0,
        error instanceof Error ? error.message : String(error)
      );

      return {
        success: false,
        error: "A apărut o eroare neașteptată la crearea activului",
        code: "UNEXPECTED_ERROR",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Handle API errors and convert them to user-friendly Romanian messages
   */
  private handleAPIError(
    error: unknown,
    fallbackMessage: string
  ): AssetCreationError {
    if (error instanceof ExternalAPIValidationError) {
      return {
        success: false,
        error: error.message,
        code: "VALIDATION_ERROR",
        details: error.validationErrors,
      };
    }

    if (error instanceof ExternalAPIError) {
      let errorMessage = error.message;
      let errorCode = "API_ERROR";

      // Map specific error codes to Romanian messages
      if (error.statusCode === 404) {
        errorMessage = "Simbolul nu a fost găsit în sursele de date externe";
        errorCode = "TICKER_NOT_FOUND";
      } else if (error.statusCode === 429) {
        errorMessage =
          "Prea multe cereri. Te rugăm să încerci din nou mai târziu";
        errorCode = "RATE_LIMITED";
      } else if (error.statusCode === 401 || error.statusCode === 403) {
        errorMessage = "Eroare de autentificare cu API-ul extern";
        errorCode = "AUTH_ERROR";
      } else if (error.statusCode && error.statusCode >= 500) {
        errorMessage =
          "Serviciul extern nu a putut procesa cererea. Verifică simbolul introdus și încearcă din nou.";
        errorCode = "SERVICE_UNAVAILABLE";
      }

      return {
        success: false,
        error: errorMessage,
        code: errorCode,
        details: error.response,
      };
    }

    return {
      success: false,
      error: fallbackMessage,
      code: "UNKNOWN_ERROR",
      details: error instanceof Error ? error.message : String(error),
    };
  }

  /**
   * Check if a ticker exists in the external API without creating it
   */
  async checkTickerExists(ticker: string): Promise<boolean> {
    try {
      if (!isValidTickerFormat(ticker)) {
        return false;
      }

      // Handle .US suffix tickers - remove suffix for processing
      const isUSTicker = ticker.toUpperCase().endsWith(".US");
      const processedTicker = isUSTicker ? ticker.slice(0, -3) : ticker;
      const normalizedTicker = normalizeTicker(processedTicker);

      // For .US tickers, use yfinance provider only
      const providers = isUSTicker ? ["yfinance"] : undefined;

      await this.apiClient.getAssetData(normalizedTicker, {
        refresh: false,
        useMock: false,
        providers,
      });

      return true;
    } catch (error) {
      if (error instanceof ExternalAPIError && error.statusCode === 404) {
        return false;
      }
      // For other errors, we can't determine if it exists, so return false
      return false;
    }
  }
}

/**
 * Create a default instance of the Asset Creation Service
 */
export function createAssetCreationService(): AssetCreationService {
  return new AssetCreationService();
}

/**
 * Convenience function to create an asset
 */
export async function createAsset(
  ticker: string,
  options?: AssetCreationOptions
): Promise<AssetCreationResponse> {
  const service = createAssetCreationService();
  return service.createAsset(ticker, options);
}

/**
 * Convenience function to check if a ticker exists
 */
export async function checkTickerExists(ticker: string): Promise<boolean> {
  const service = createAssetCreationService();
  return service.checkTickerExists(ticker);
}
