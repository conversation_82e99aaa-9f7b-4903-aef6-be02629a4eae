import { PortfolioEditClient } from "@/components/portfolios/portfolio-edit-client";
import { auth } from "@/lib/auth";
import { getPortfolioById } from "@/utils/db/portfolio-queries";
import { Metadata } from "next";
import { headers } from "next/headers";
import { notFound, redirect } from "next/navigation";

interface PortfolioEditPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({
  params,
}: PortfolioEditPageProps): Promise<Metadata> {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return {
        title: "Editare Portofoliu - Portavio",
        description: "Editează detaliile portofoliului tău cu Portavio.",
      };
    }

    const { id } = await params;
    const portfolio = await getPortfolioById(id);

    if (!portfolio || portfolio.user_id !== session.user.id) {
      return {
        title: "Portofoli<PERSON> - Portavio",
        description: "Portofoliul căutat nu a fost găsit.",
      };
    }

    return {
      title: `Editare ${portfolio.name} - Portavio`,
      description: `Editează detaliile portofoliului ${portfolio.name} cu Portavio.`,
      keywords: [
        "editare portofoliu",
        "modificare portofoliu",
        "management portofoliu",
        "portavio",
        "investiții",
      ],
      openGraph: {
        title: `Editare ${portfolio.name} - Portavio`,
        description: `Editează detaliile portofoliului ${portfolio.name} cu Portavio.`,
        type: "website",
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Editare Portofoliu - Portavio",
      description: "Editează detaliile portofoliului tău cu Portavio.",
    };
  }
}

export default async function PortfolioEditPage({
  params,
}: PortfolioEditPageProps) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    redirect("/auth/signin");
  }

  const { id } = await params;
  let portfolio;
  try {
    portfolio = await getPortfolioById(id);
  } catch (error) {
    console.error("Error fetching portfolio:", error);
    notFound();
  }

  if (!portfolio) {
    notFound();
  }

  // Check if user owns this portfolio
  if (portfolio.user_id !== session.user.id) {
    redirect("/portfolios");
  }

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen max-w-4xl">
      <PortfolioEditClient portfolio={portfolio} />
    </div>
  );
}
