import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import {
  createTransaction,
  getPortfolioById,
} from "@/utils/db/portfolio-queries";
import {
  transactionInputSchema,
  TransactionInputData,
} from "@/lib/transaction-schemas";
import { z } from "zod";

// POST /api/transactions - Create a new transaction
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate request body
    let validatedData: TransactionInputData;
    try {
      validatedData = transactionInputSchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Date invalide", details: error.errors },
          { status: 400 }
        );
      }
      throw error;
    }

    // Verify that the portfolio belongs to the user
    const portfolio = await getPortfolioById(validatedData.portfolioId);
    if (!portfolio) {
      return NextResponse.json(
        { error: "Portofoliul nu a fost găsit" },
        { status: 404 }
      );
    }

    if (portfolio.user_id !== session.user.id) {
      return NextResponse.json(
        { error: "Nu ai permisiunea să accesezi acest portofoliu" },
        { status: 403 }
      );
    }

    // Create the transaction
    const transaction = await createTransaction(
      validatedData.portfolioId,
      validatedData.ticker,
      validatedData.quantity,
      validatedData.transactionDate,
      validatedData.type,
      validatedData.notes
    );

    return NextResponse.json({
      transaction,
      message: "Tranzacția a fost creată cu succes",
    });
  } catch (error) {
    console.error("Error creating transaction:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Date invalide", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Nu s-a putut crea tranzacția" },
      { status: 500 }
    );
  }
}
